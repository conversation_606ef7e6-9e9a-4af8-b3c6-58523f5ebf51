# Layout System Documentation

## Overview
The application now has a flexible layout system that allows you to control which pages show the sidebar, header, and footer components.

## How It Works

### Automatic Layout Detection
The system automatically detects admin routes and hides the main website layout (sidebar, header, footer) for them.

### Current Admin Routes (No Sidebar/Header/Footer)
- `/admin`
- `/admin-dashboard`
- `/admin-debug`
- **Note**: Admin routes are only accessible without language prefixes (English only)

### Current Pages Without Sidebar (But With Header/Footer)
- `/about`
- Any language-prefixed versions (e.g., `/en/about`, `/pt/about`)

## Creating Pages Without Sidebar

### Method 1: Add to Configuration (Recommended)
Edit `frontend/src/hooks/useLayoutConfig.js` and add your route to the appropriate array:

```javascript
export const LAYOUT_CONFIG = {
  // Admin routes (no header, footer, or sidebar)
  ADMIN_ROUTES: [
    '/admin',
    '/admin-dashboard',
    '/admin-debug',
  ],
  // Pages that should not have sidebar but keep header/footer
  NO_SIDEBAR_ONLY_ROUTES: [
    '/about',
    '/contact',     // Add your new route here
    '/privacy',     // Add your new route here
    '/terms',       // Add your new route here
  ]
};
```

### Method 2: Using the Hook in Components
You can also use the `useLayoutConfig` hook in your components to conditionally render content:

```javascript
import { useLayoutConfig } from '../hooks/useLayoutConfig';

function MyComponent() {
  const { shouldShowSidebar, shouldShowHeader, shouldShowFooter, isAdminRoute } = useLayoutConfig();
  
  if (isAdminRoute) {
    // Render admin-specific layout
    return <AdminLayout />;
  }
  
  // Regular component rendering
  return <RegularLayout />;
}
```

## Layout Configuration Options

The `useLayoutConfig` hook returns:
- `shouldShowSidebar`: Boolean - whether to show the main sidebar
- `shouldShowHeader`: Boolean - whether to show the main header
- `shouldShowFooter`: Boolean - whether to show the main footer  
- `isAdminRoute`: Boolean - whether current route is an admin route
- `currentPath`: String - the current pathname

## Examples

### Creating a Full-Screen Game Page
```javascript
// Add '/game-fullscreen' to NO_SIDEBAR_ROUTES, NO_HEADER_ROUTES, NO_FOOTER_ROUTES
// The page will render without any main website chrome
```

### Creating a Landing Page
```javascript
// Add '/landing' to NO_SIDEBAR_ROUTES only
// The page will have header and footer but no sidebar
```

### Creating a Modal-Style Page
```javascript
// Add '/modal-page' to all three arrays
// The page will render completely clean without any main website elements
```

## Benefits

1. **Centralized Control**: All layout logic is in one place
2. **Language Support**: Automatically handles language-prefixed routes
3. **Flexible**: Easy to add new routes or modify existing ones
4. **Maintainable**: Clear separation of concerns
5. **Extensible**: Easy to add new layout options in the future

## Migration Notes

- Admin pages now automatically render without the main website sidebar
- No changes needed to existing admin components
- The system is backward compatible with existing routes
